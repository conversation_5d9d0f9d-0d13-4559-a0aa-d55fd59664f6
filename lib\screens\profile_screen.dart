import 'package:flutter/material.dart';
import '../utils/colors.dart';
import '../widgets/profile_stat_card.dart';
import '../widgets/profile_menu_item.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Stack(
        children: [
          // Custom curved background
          CustomPaint(
            size: Size(
              MediaQuery.of(context).size.width,
              MediaQuery.of(context).size.height * 0.6,
            ),
            painter: CurvedBackgroundPainter(),
          ),

          // Content
          SafeArea(
            child: Column(
              children: [
                // App bar
                Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 16,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      GestureDetector(
                        onTap: () => Navigator.pop(context),
                        child: Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Icon(
                            Icons.arrow_back,
                            color: Colors.white,
                            size: 20,
                          ),
                        ),
                      ),
                      const Text(
                        'Profile',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Icon(
                          Icons.edit,
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 80),
              ],
            ),
          ),

          // Profile content positioned to overlap blue and white sections
          Positioned(
            top: MediaQuery.of(context).size.height * 0.25,
            left: 0,
            right: 0,
            child: Column(
              children: [
                // Profile picture
                Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.white, width: 4),
                  ),
                  child: ClipOval(
                    child: Image.network(
                      'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
                      width: 120,
                      height: 120,
                      fit: BoxFit.cover,
                      loadingBuilder: (context, child, loadingProgress) {
                        if (loadingProgress == null) return child;
                        return Container(
                          width: 120,
                          height: 120,
                          decoration: const BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [Color(0xFF6B73FF), Color(0xFF9DD5EA)],
                            ),
                          ),
                          child: const Center(
                            child: CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 2,
                            ),
                          ),
                        );
                      },
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          width: 120,
                          height: 120,
                          decoration: const BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [Color(0xFF6B73FF), Color(0xFF9DD5EA)],
                            ),
                          ),
                          child: const Icon(
                            Icons.person,
                            size: 60,
                            color: Colors.white,
                          ),
                        );
                      },
                    ),
                  ),
                ),

                const SizedBox(height: 20),

                // User name
                const Text(
                  'Mohamed Shaban',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),

                const SizedBox(height: 8),

                // Username
                const Text(
                  '@MyPatronasIsAcalculator',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w400,
                    color: Colors.white70,
                  ),
                ),

                const SizedBox(height: 16),

                // Bio text
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 40),
                  child: Text(
                    'In a laoreet purus. Integer turpis quam, laoreet id orci nec, ultrices lacinia nunc. Aliquam erat vo',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      color: Colors.white70,
                      height: 1.4,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // White content area with rounded top corners
          Positioned(
            top: MediaQuery.of(context).size.height * 0.55,
            left: 0,
            right: 0,
            bottom: 0,
            child: Container(
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(40),
                  topRight: Radius.circular(40),
                ),
              ),
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    const SizedBox(height: 60),

                    // Stats section
                    Container(
                      margin: const EdgeInsets.symmetric(horizontal: 24),
                      padding: const EdgeInsets.symmetric(vertical: 20),
                      decoration: BoxDecoration(
                        color: AppColors.primaryBlue,
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 10,
                            offset: const Offset(0, 5),
                          ),
                        ],
                      ),
                      child: const Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          ProfileStatCard(number: '32', label: 'Courses'),
                          ProfileStatCard(number: '150', label: 'Credits'),
                          ProfileStatCard(number: '40', label: 'Achievement'),
                        ],
                      ),
                    ),

                    const SizedBox(height: 40),

                    // Menu items
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24),
                      child: Column(
                        children: [
                          ProfileMenuItem(
                            icon: Icons.bookmark_outline,
                            title: 'Saved Courses',
                            onTap: () {},
                          ),
                          const SizedBox(height: 16),
                          ProfileMenuItem(
                            icon: Icons.notifications_outlined,
                            title: 'Notification',
                            onTap: () {},
                          ),
                          const SizedBox(height: 16),
                          ProfileMenuItem(
                            icon: Icons.credit_card_outlined,
                            title: 'Payment Method',
                            onTap: () {},
                          ),
                          const SizedBox(height: 16),
                          ProfileMenuItem(
                            icon: Icons.logout,
                            title: 'Log Out',
                            onTap: () {
                              Navigator.pushNamedAndRemoveUntil(
                                context,
                                '/login',
                                (route) => false,
                              );
                            },
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 40),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class CurvedBackgroundPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..shader = const LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [Color(0xFF4A90E2), Color(0xFF357ABD)],
      ).createShader(Rect.fromLTWH(0, 0, size.width, size.height));

    final path = Path();

    // Start from top left
    path.moveTo(0, 0);

    // Top edge
    path.lineTo(size.width, 0);

    // Right edge going down
    path.lineTo(size.width, size.height * 0.65);

    // Create more organic flowing curves like in the design
    path.cubicTo(
      size.width * 0.9,
      size.height * 0.75,
      size.width * 0.7,
      size.height * 0.85,
      size.width * 0.5,
      size.height * 0.8,
    );

    path.cubicTo(
      size.width * 0.3,
      size.height * 0.75,
      size.width * 0.15,
      size.height * 0.9,
      0,
      size.height * 0.85,
    );

    // Left edge going up
    path.lineTo(0, 0);

    path.close();

    canvas.drawPath(path, paint);

    // Add some flowing decorative shapes
    final decorativePaint = Paint()
      ..color = Colors.white.withValues(alpha: 0.1)
      ..style = PaintingStyle.fill;

    // Large flowing shape
    final decorativePath1 = Path();
    decorativePath1.moveTo(size.width * 0.8, size.height * 0.1);
    decorativePath1.cubicTo(
      size.width * 1.2,
      size.height * 0.2,
      size.width * 1.1,
      size.height * 0.4,
      size.width * 0.9,
      size.height * 0.5,
    );
    decorativePath1.cubicTo(
      size.width * 0.7,
      size.height * 0.6,
      size.width * 0.6,
      size.height * 0.3,
      size.width * 0.8,
      size.height * 0.1,
    );
    canvas.drawPath(decorativePath1, decorativePaint);

    // Smaller flowing shape
    final decorativePath2 = Path();
    decorativePath2.moveTo(size.width * 0.1, size.height * 0.3);
    decorativePath2.cubicTo(
      size.width * 0.3,
      size.height * 0.25,
      size.width * 0.4,
      size.height * 0.45,
      size.width * 0.2,
      size.height * 0.6,
    );
    decorativePath2.cubicTo(
      size.width * 0.0,
      size.height * 0.75,
      size.width * -0.1,
      size.height * 0.5,
      size.width * 0.1,
      size.height * 0.3,
    );
    canvas.drawPath(decorativePath2, decorativePaint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
